2025-07-31 13:54:11,913 - __main__ - INFO - Starting up Search API...
2025-07-31 13:54:11,914 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-31 13:54:11,916 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-31 13:54:11,916 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-31 13:54:13,786 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-07-31 13:54:15,393 - __main__ - INFO - Model loaded successfully
2025-07-31 13:54:15,393 - __main__ - INFO - Initializing database connection...
2025-07-31 13:54:15,509 - __main__ - INFO - Database connection established
2025-07-31 13:54:15,510 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-31 13:54:15,510 - __main__ - INFO - Cache file missing: search_cache/embeddings.dat
2025-07-31 13:54:15,510 - __main__ - INFO - Cache files not found, generating new cache from database...
2025-07-31 13:54:15,511 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-07-31 13:54:15,529 - __main__ - INFO - Total records to process: 2000
2025-07-31 13:54:15,531 - __main__ - INFO - Processing chunk 1: records 0-2000
2025-07-31 13:54:16,929 - __main__ - INFO - Progress: 2000/2000 records processed (100.0%)
2025-07-31 13:54:16,930 - __main__ - INFO - Saving BM25 index to disk: 11612 terms, 2000 documents
2025-07-31 13:54:17,155 - __main__ - INFO - BM25 index saved to disk
2025-07-31 13:54:22,818 - __main__ - INFO - Data loading complete: 2000 records saved to search_cache
2025-07-31 13:54:22,833 - __main__ - INFO - Chunked cache loading completed in 7.32s
2025-07-31 13:54:22,834 - __main__ - INFO - Loaded 2000 records with memory-mapped storage
2025-07-31 13:54:22,963 - __main__ - INFO - Search cache loaded: 2000 records
2025-07-31 13:54:22,963 - __main__ - INFO - Memory-mapped storage: True
2025-07-31 13:54:22,963 - __main__ - INFO - Starting system monitor...
2025-07-31 13:54:22,964 - __main__ - INFO - 
System monitor started

2025-07-31 13:54:22,964 - __main__ - INFO - Starting cache update monitor...
2025-07-31 13:54:22,965 - __main__ - INFO - Cache update monitor started
2025-07-31 13:54:22,966 - __main__ - INFO - Starting comprehensive incremental cache update
2025-07-31 13:54:22,966 - __main__ - INFO - Creating temporary table with 2000 cached IDs...
2025-07-31 13:54:22,967 - __main__ - INFO - Inserting batch 1/2 of cache IDs...
2025-07-31 13:54:22,967 - __main__ - INFO - Inserting batch 2/2 of cache IDs...
2025-07-31 13:54:23,175 - __main__ - INFO - Found 2000 missing records to add to cache
2025-07-31 13:54:23,175 - __main__ - INFO - Loading missing records with chunk size 5000
2025-07-31 13:54:23,224 - __main__ - INFO - Loading 2000 missing records in chunks
2025-07-31 13:54:23,225 - __main__ - INFO - Processing missing records chunk 1: records 0-2000
2025-07-31 13:54:24,352 - __main__ - INFO - Missing records progress: 2000/2000 records processed (100.0%)
2025-07-31 13:54:24,353 - __main__ - INFO - Missing records loading complete: 2000 records loaded
2025-07-31 13:54:24,355 - __main__ - INFO - Starting incremental cache update with 2000 new records
2025-07-31 13:54:24,355 - __main__ - INFO - Extending embeddings from 2000 to 4000 records
2025-07-31 13:54:24,485 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-31 13:54:24,510 - __main__ - INFO - Embeddings file extended successfully to 4000 records
2025-07-31 13:54:24,511 - __main__ - INFO - Adding 2000 documents to BM25 index incrementally (full)...
2025-07-31 13:54:25,017 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-31 13:54:26,076 - __main__ - INFO - Incremental cache update completed in 1.72s
2025-07-31 13:54:26,076 - __main__ - INFO - Cache now contains 4000 total records
2025-07-31 13:54:26,212 - __main__ - INFO - Cleaned up temporary table: ReportNLP_cache_ids
2025-07-31 13:54:26,213 - __main__ - INFO - Comprehensive incremental cache update completed successfully in 3.25s: {'success': True, 'new_records_added': 2000, 'total_records': 4000, 'update_time_seconds': 1.721135139465332}
2025-07-31 13:54:47,447 - __main__ - INFO - Shutting down Search API...
2025-07-31 13:54:49,454 - __main__ - INFO - Cache update monitor stopped
2025-07-31 13:54:50,461 - __main__ - INFO - System monitor stopped
2025-07-31 13:54:50,463 - __main__ - INFO - Database connection closed
2025-07-31 14:44:47,916 - backend_api - INFO - LMDB BM25 index finalized: 5 documents, avg_length=7.2
2025-07-31 14:44:48,173 - backend_api - INFO - Loading BM25 metadata from LMDB...
2025-07-31 14:44:48,174 - backend_api - INFO - LMDB BM25 metadata loaded: 5 documents, avg_length=7.2
2025-07-31 14:44:48,177 - backend_api - INFO - Adding 1 documents to LMDB BM25 index incrementally...
2025-07-31 14:44:48,178 - backend_api - INFO - LMDB BM25 index updated: 5 total documents, avg_length=7.0
2025-07-31 14:45:53,351 - backend_api - INFO - LMDB BM25 index finalized: 5 documents, avg_length=7.2
2025-07-31 14:45:53,611 - backend_api - INFO - Loading BM25 metadata from LMDB...
2025-07-31 14:45:53,611 - backend_api - INFO - LMDB BM25 metadata loaded: 5 documents, avg_length=7.2
2025-07-31 14:45:53,613 - backend_api - INFO - Adding 1 documents to LMDB BM25 index incrementally...
2025-07-31 14:45:53,614 - backend_api - INFO - LMDB BM25 index updated: 5 total documents, avg_length=7.0
2025-07-31 14:46:50,118 - backend_api - INFO - LMDB BM25 index finalized: 5 documents, avg_length=7.2
2025-07-31 14:46:50,378 - backend_api - INFO - Loading BM25 metadata from LMDB...
2025-07-31 14:46:50,378 - backend_api - INFO - LMDB BM25 metadata loaded: 5 documents, avg_length=7.2
2025-07-31 14:46:50,380 - backend_api - INFO - Adding 1 documents to LMDB BM25 index incrementally...
2025-07-31 14:46:50,381 - backend_api - INFO - LMDB BM25 index updated: 5 total documents, avg_length=7.0
2025-07-31 14:47:52,531 - backend_api - INFO - LMDB BM25 index finalized: 5 documents, avg_length=7.2
2025-07-31 14:47:52,786 - backend_api - INFO - Loading BM25 metadata from LMDB...
2025-07-31 14:47:52,786 - backend_api - INFO - LMDB BM25 metadata loaded: 5 documents, avg_length=7.2
2025-07-31 14:47:52,788 - backend_api - INFO - Adding 1 documents to LMDB BM25 index incrementally...
2025-07-31 14:47:52,790 - backend_api - INFO - LMDB BM25 index updated: 5 total documents, avg_length=7.0
