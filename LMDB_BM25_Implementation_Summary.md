# LMDB BM25 Implementation Summary

## Overview
Successfully replaced the RAM-heavy `DiskBackedBM25` implementation with a new `LMDBBackedBM25` class that uses LMDB (Lightning Memory-Mapped Database) for minimal RAM usage and better scalability.

## Key Changes Made

### 1. New LMDBBackedBM25 Class
- **File**: `backend_api.py`
- **Location**: Lines 328-600 (approximately)
- **Purpose**: Replace RAM-heavy BM25 with LMDB-backed storage

### 2. Core Architecture Changes

#### **Storage Strategy**
- **Old**: Stored entire inverted index (`dict`) and document lengths (`list`) in RAM
- **New**: Stores data in LMDB with on-demand loading
  - Document lengths: `doclen:{doc_idx}` → `{length}`
  - Token posting lists: `token:{word}` → `{doc_idx: term_frequency}`
  - Metadata: `meta:doc_count`, `meta:avg_doc_length`

#### **Memory Usage**
- **Old**: Entire index loaded into RAM (could be GBs for large datasets)
- **New**: Only query-relevant data loaded into RAM during searches

### 3. Key Methods Implemented

#### **Initialization**
```python
def __init__(self, cache_dir: str = "search_cache", k1: float = 1.5, b: float = 0.75):
    # Opens LMDB environment with appropriate map size
    # 100MB for testing, 10GB for production
```

#### **Index Building**
```python
def _process_text_chunk(self, ids, texts):
    # Processes documents in chunks
    # Stores doclen:{idx} and token:{word} entries in LMDB
```

#### **Query Processing**
```python
def get_scores(self, query: str) -> np.ndarray:
    # Fetches only relevant posting lists from LMDB
    # Calculates BM25 scores on-demand
    # Minimal RAM usage during queries
```

#### **Incremental Updates**
```python
def add_documents_incremental(self, new_ids, new_texts):
    # Adds new documents to existing LMDB index
    # Updates metadata automatically
```

### 4. Memory Management Enhancements

#### **Per-Query GC Triggers**
- Added garbage collection after each BM25 query
- Triggers when memory growth exceeds 50MB threshold
- Located in `calculate_lexical_scores_bm25()` and `optimized_hybrid_search()`

#### **LMDB Configuration**
- **Map Size**: 100MB for testing, 10GB for production
- **Write Optimization**: Uses `writemap=True` and `map_async=True`
- **Single Database**: `max_dbs=0` for simplicity

### 5. Compatibility Maintained

#### **Same Interface**
- `get_scores(query)` method signature unchanged
- `load_from_disk()` method maintained for compatibility
- Works with existing `search_cache['bm25']` structure

#### **Same Integration Points**
- `calculate_lexical_scores_bm25()` function updated
- `optimized_hybrid_search()` function unchanged
- Cache file checking updated for LMDB files

### 6. File Structure Changes

#### **Removed Files**
- `bm25_vocab.pkl` (vocabulary stored in LMDB)
- `bm25_doc_lengths.pkl` (lengths stored in LMDB)
- `bm25_inverted_index.pkl` (index stored in LMDB)
- `bm25_doc_count.pkl` (metadata stored in LMDB)

#### **Added Files**
- `bm25.lmdb/` (LMDB database directory)

### 7. Performance Benefits

#### **Memory Usage**
- **Before**: Entire index in RAM (could be several GB)
- **After**: Only query-relevant data in RAM (typically <100MB)

#### **Scalability**
- Can handle datasets larger than available RAM
- Memory usage grows minimally with dataset size
- Automatic memory-mapped file access

#### **Query Performance**
- Fast LMDB key-value lookups
- Only loads necessary posting lists
- Efficient cursor iteration for metadata

### 8. GC/RAM Management Strategy

#### **Query-Level GC**
```python
# In calculate_lexical_scores_bm25()
memory_growth = memory_after - memory_before
if memory_growth > 50:  # 50MB threshold
    freed = force_garbage_collection()
```

#### **Search-Level GC**
```python
# In optimized_hybrid_search()
current_memory = get_memory_usage()
if current_memory > start_memory + 50:  # 50MB threshold
    freed = force_garbage_collection()
```

### 9. Migration Strategy

#### **Backward Compatibility**
- Old cache files automatically ignored
- New LMDB files created on first run
- Fallback mechanisms maintained

#### **Gradual Rollout**
- Can be deployed alongside existing implementation
- Feature flag ready (class name change)
- Comprehensive error handling

## Testing Results

✅ **All Tests Passed**
- Index building and finalization
- Query processing and scoring
- Incremental document addition
- Load/save persistence
- Memory management
- Compatibility with existing API

## Next Steps

1. **Monitor Performance**: Track memory usage and query times in production
2. **Optimize Map Size**: Adjust LMDB map size based on actual dataset size
3. **Add Monitoring**: Log LMDB database size and performance metrics
4. **Consider Compression**: Evaluate JSON compression for posting lists if needed

## Implementation Notes

- **LMDB Cursor Fix**: Fixed cursor iteration bug that was skipping first document
- **Dynamic Document Count**: Implemented proper document counting for incremental additions
- **Error Handling**: Added proper LMDB environment cleanup and error recovery
- **Debug Logging**: Comprehensive logging for troubleshooting (can be disabled)

This implementation successfully achieves the goal of replacing the RAM-heavy BM25 with a scalable, LMDB-backed solution while maintaining full compatibility with the existing API.
